<?php
/* SVN FILE: $Id$ */
/* App schema generated on: 2009-04-15 10:04:36 : 1239788136*/
class AppSchema extends CakeSchema {
	var $name = 'App';

	function before($event = array()) {
		return true;
	}

	function after($event = array()) {
	}

	var $accommodation = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'name' => array('type' => 'string', 'null' => false),
			'slug' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'location' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'image_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'official_rating' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'b_v_rating' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'from_price' => array('type' => 'float', 'null' => true, 'default' => NULL, 'length' => 8),
			'resort_fee_applicable' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'minimum_stay' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'b_v_added_value' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'our_recommendation' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'preferred_status' => array('type' => 'boolean', 'null' => false, 'default' => '0'),
			'highlights' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'accommodation_type' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'latitude' => array('type' => 'float', 'null' => true, 'default' => NULL, 'length' => 11),
			'longitude' => array('type' => 'float', 'null' => true, 'default' => NULL, 'length' => 11),
			'map_latitude' => array('type' => 'float', 'null' => true, 'default' => NULL, 'length' => 11),
			'map_longitude' => array('type' => 'float', 'null' => true, 'default' => NULL, 'length' => 11),
			'zoom_level' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 2),
			'meta_title' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'meta_description' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'meta_keywords' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'summary' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'published' => array('type' => 'boolean', 'null' => false, 'default' => '0'),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $accommodation_accommodation_characteristics = array(
			'accommodation_id' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'primary'),
			'accommodation_characteristic_id' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'primary'),
			'indexes' => array('accommodation_id' => array('column' => array('accommodation_id', 'accommodation_characteristic_id'), 'unique' => 1))
		);
	var $accommodation_accommodation_facilities = array(
			'accommodation_id' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'primary'),
			'accommodation_facility_id' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'primary'),
			'indexes' => array('accommodation_id' => array('column' => array('accommodation_id', 'accommodation_facility_id'), 'unique' => 1))
		);
	var $accommodation_characteristics = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'name' => array('type' => 'string', 'null' => false),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $accommodation_destinations = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'accommodation_id' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'index'),
			'destination_id' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'index'),
			'order' => array('type' => 'integer', 'null' => false, 'length' => 10),
			'featured' => array('type' => 'boolean', 'null' => false, 'default' => '0'),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'destination_id' => array('column' => 'destination_id', 'unique' => 0), 'accommodation_id' => array('column' => 'accommodation_id', 'unique' => 0))
		);
	var $accommodation_facilities = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'name' => array('type' => 'string', 'null' => false),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $accommodation_holiday_types = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'accommodation_id' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'index'),
			'holiday_type_id' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'index'),
			'order' => array('type' => 'integer', 'null' => false, 'length' => 10),
			'featured' => array('type' => 'boolean', 'null' => false, 'default' => '0'),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'destination_id' => array('column' => 'holiday_type_id', 'unique' => 0), 'accommodation_id' => array('column' => 'accommodation_id', 'unique' => 0))
		);
	var $accommodation_images = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'accommodation_id' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'index'),
			'image_id' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'index'),
			'order' => array('type' => 'integer', 'null' => false, 'length' => 10),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'image_id' => array('column' => 'image_id', 'unique' => 0), 'destination_id' => array('column' => 'accommodation_id', 'unique' => 0))
		);
	var $acos = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'parent_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10, 'key' => 'index'),
			'model' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'foreign_key' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10, 'key' => 'index'),
			'alias' => array('type' => 'string', 'null' => true, 'default' => NULL, 'key' => 'index'),
			'lft' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10, 'key' => 'index'),
			'rght' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'alias' => array('column' => 'alias', 'unique' => 0), 'parent_id' => array('column' => 'parent_id', 'unique' => 0), 'acos_idx1' => array('column' => array('lft', 'rght'), 'unique' => 0), 'foreign_key' => array('column' => array('foreign_key', 'lft'), 'unique' => 0))
		);
	var $activities = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'name' => array('type' => 'string', 'null' => false),
			'slug' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'image_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10, 'key' => 'index'),
			'meta_title' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'meta_description' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'meta_keywords' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'summary' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'published' => array('type' => 'boolean', 'null' => false, 'default' => '0'),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'image_id' => array('column' => 'image_id', 'unique' => 0))
		);
	var $activities_destinations = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'activity_id' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'index'),
			'destination_id' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'index'),
			'order' => array('type' => 'integer', 'null' => false, 'length' => 10),
			'featured' => array('type' => 'boolean', 'null' => false, 'default' => '0'),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'activity_id' => array('column' => 'activity_id', 'unique' => 0), 'destination_id' => array('column' => 'destination_id', 'unique' => 0))
		);
	var $activities_holiday_types = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'activity_id' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'index'),
			'holiday_type_id' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'index'),
			'order' => array('type' => 'integer', 'null' => false, 'length' => 10),
			'featured' => array('type' => 'boolean', 'null' => false, 'default' => '0'),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'activity_id' => array('column' => 'activity_id', 'unique' => 0), 'destination_id' => array('column' => 'holiday_type_id', 'unique' => 0))
		);
	var $aros = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'parent_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'model' => array('type' => 'string', 'null' => true, 'default' => NULL, 'key' => 'index'),
			'foreign_key' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'alias' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'lft' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'rght' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'aro' => array('column' => array('model', 'foreign_key'), 'unique' => 0))
		);
	var $aros_acos = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'aro_id' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'index'),
			'aco_id' => array('type' => 'integer', 'null' => false, 'length' => 10),
			'_create' => array('type' => 'string', 'null' => false, 'default' => '0', 'length' => 2),
			'_read' => array('type' => 'string', 'null' => false, 'default' => '0', 'length' => 2),
			'_update' => array('type' => 'string', 'null' => false, 'default' => '0', 'length' => 2),
			'_delete' => array('type' => 'string', 'null' => false, 'default' => '0', 'length' => 2),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'ARO_ACO_KEY' => array('column' => array('aro_id', 'aco_id'), 'unique' => 1))
		);
	var $contacts = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'type' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'name' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'email' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'telephone' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'preferred_day' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'preferred_time' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'optin' => array('type' => 'boolean', 'null' => false, 'default' => '0'),
			'question' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $content_blocks = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'image_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10, 'key' => 'index'),
			'alignment' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'content' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'model' => array('type' => 'string', 'null' => false),
			'modelid' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'index'),
			'link_text' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'link' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'order' => array('type' => 'integer', 'null' => false, 'length' => 10),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'image_id' => array('column' => 'image_id', 'unique' => 0), 'model_id' => array('column' => 'modelid', 'unique' => 0))
		);
	var $custom_image_versions = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'image_id' => array('type' => 'integer', 'null' => false, 'length' => 10),
			'image_version_id' => array('type' => 'integer', 'null' => false, 'length' => 10),
			'source_left_offset' => array('type' => 'integer', 'null' => false, 'length' => 10),
			'source_top_offset' => array('type' => 'integer', 'null' => false, 'length' => 10),
			'source_width' => array('type' => 'integer', 'null' => false, 'length' => 10),
			'source_height' => array('type' => 'integer', 'null' => false, 'length' => 10),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $destinations = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'parent_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'lft' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'rght' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'child_count' => array('type' => 'integer', 'null' => false, 'default' => '0', 'length' => 10),
			'direct_child_count' => array('type' => 'integer', 'null' => false, 'default' => '0', 'length' => 10),
			'latitude' => array('type' => 'float', 'null' => true, 'default' => NULL, 'length' => 11),
			'longitude' => array('type' => 'float', 'null' => true, 'default' => NULL, 'length' => 11),
			'map_latitude' => array('type' => 'float', 'null' => true, 'default' => NULL, 'length' => 11),
			'map_longitude' => array('type' => 'float', 'null' => true, 'default' => NULL, 'length' => 11),
			'zoom_level' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 2),
			'name' => array('type' => 'string', 'null' => false),
			'slug' => array('type' => 'string', 'null' => true, 'default' => NULL, 'key' => 'unique'),
			'summary' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'main_image_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'meta_title' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'meta_description' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'meta_keywords' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'published' => array('type' => 'boolean', 'null' => false, 'default' => '0'),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'slug' => array('column' => 'slug', 'unique' => 1))
		);
	var $destinations_images = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'destination_id' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'index'),
			'image_id' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'index'),
			'order' => array('type' => 'integer', 'null' => false, 'length' => 10),
			'featured' => array('type' => 'boolean', 'null' => false, 'default' => '0'),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'destination_id' => array('column' => 'destination_id', 'unique' => 0), 'image_id' => array('column' => 'image_id', 'unique' => 0))
		);
	var $destinations_itineraries = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'destination_id' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'index'),
			'itinerary_id' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'index'),
			'order' => array('type' => 'integer', 'null' => false, 'length' => 10),
			'featured' => array('type' => 'boolean', 'null' => false, 'default' => '0'),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'destination_id' => array('column' => 'destination_id', 'unique' => 0), 'itinerary_id' => array('column' => 'itinerary_id', 'unique' => 0))
		);
	var $destinations_on_holiday_types = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'destination_id' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'index'),
			'holiday_type_id' => array('type' => 'integer', 'null' => false, 'length' => 10),
			'order' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'featured' => array('type' => 'boolean', 'null' => false, 'default' => '0'),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'UNIQUE' => array('column' => array('destination_id', 'holiday_type_id'), 'unique' => 1), 'destination_id' => array('column' => 'destination_id', 'unique' => 0))
		);
	var $groups = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'name' => array('type' => 'string', 'null' => false),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $holiday_types = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'name' => array('type' => 'string', 'null' => false),
			'slug' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'image_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10, 'key' => 'index'),
			'meta_title' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'meta_description' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'meta_keywords' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'order' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'summary' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'published' => array('type' => 'boolean', 'null' => false, 'default' => '0'),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'image_id' => array('column' => 'image_id', 'unique' => 0))
		);
	var $holiday_types_itineraries = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'holiday_type_id' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'index'),
			'itinerary_id' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'index'),
			'order' => array('type' => 'integer', 'null' => false, 'length' => 10),
			'featured' => array('type' => 'boolean', 'null' => false, 'default' => '0'),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'destination_id' => array('column' => 'holiday_type_id', 'unique' => 0), 'itinerary_id' => array('column' => 'itinerary_id', 'unique' => 0))
		);
	var $holiday_types_on_destinations = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'destination_id' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'index'),
			'holiday_type_id' => array('type' => 'integer', 'null' => false, 'length' => 10),
			'order' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'featured' => array('type' => 'boolean', 'null' => false, 'default' => '0'),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'UNIQUE' => array('column' => array('destination_id', 'holiday_type_id'), 'unique' => 1), 'destination_id' => array('column' => 'destination_id', 'unique' => 0))
		);
	var $image_folders = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'parent_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'lft' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'rght' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'child_count' => array('type' => 'integer', 'null' => false, 'default' => '0', 'length' => 10),
			'direct_child_count' => array('type' => 'integer', 'null' => false, 'default' => '0', 'length' => 10),
			'name' => array('type' => 'string', 'null' => false),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $image_versions = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'name' => array('type' => 'string', 'null' => false),
			'usage' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'type' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'width' => array('type' => 'integer', 'null' => false, 'default' => '0', 'length' => 10),
			'height' => array('type' => 'integer', 'null' => false, 'default' => '0', 'length' => 10),
			'force_resize' => array('type' => 'boolean', 'null' => false, 'default' => '0'),
			'created' => array('type' => 'datetime', 'null' => false, 'default' => '0000-00-00 00:00:00'),
			'modified' => array('type' => 'datetime', 'null' => false, 'default' => '0000-00-00 00:00:00'),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $images = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'alt' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'image_folder_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'extension' => array('type' => 'string', 'null' => false, 'length' => 4),
			'versions' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'do_not_resize' => array('type' => 'boolean', 'null' => false, 'default' => '0'),
			'created' => array('type' => 'datetime', 'null' => false, 'default' => '0000-00-00 00:00:00'),
			'modified' => array('type' => 'datetime', 'null' => false, 'default' => '0000-00-00 00:00:00'),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $itineraries = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'name' => array('type' => 'string', 'null' => false),
			'slug' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'image_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'meta_title' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'meta_description' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'meta_keywords' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'summary' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'from_price' => array('type' => 'float', 'null' => true, 'default' => NULL, 'length' => 8),
			'map_latitude' => array('type' => 'float', 'null' => false, 'default' => '0.000000', 'length' => 11),
			'map_longitude' => array('type' => 'float', 'null' => false, 'default' => '0.000000', 'length' => 11),
			'zoom_level' => array('type' => 'integer', 'null' => false, 'default' => '0', 'length' => 2),
			'published' => array('type' => 'boolean', 'null' => false, 'default' => '0'),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $itinerary_days = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'itinerary_id' => array('type' => 'integer', 'null' => false, 'length' => 10, 'key' => 'index'),
			'order' => array('type' => 'integer', 'null' => false, 'length' => 3),
			'day_number' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'name' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'location' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'miles' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'detail' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'image_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'latitude' => array('type' => 'float', 'null' => false, 'default' => '0.000000', 'length' => 11),
			'longitude' => array('type' => 'float', 'null' => false, 'default' => '0.000000', 'length' => 11),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'itinerary_id' => array('column' => 'itinerary_id', 'unique' => 0))
		);
	var $pages = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'parent_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'lft' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'rght' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'child_count' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'direct_child_count' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'css_class' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'internal_ref' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'navigation_label' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'image_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'slug' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'meta_title' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'meta_description' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'meta_keywords' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'published' => array('type' => 'boolean', 'null' => false, 'default' => '0'),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $landing_pages = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'name' => array('type' => 'string', 'null' => false),
			'slug' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'image_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'meta_title' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'meta_description' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'meta_keywords' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'youtube_playlist_id' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 16),
			'order' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'summary' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'published' => array('type' => 'boolean', 'null' => false, 'default' => '0'),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $quote_requests = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'firstname' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'lastname' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'telephone' => array('type' => 'string', 'null' => false, 'length' => 16),
			'email' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'email_optin' => array('type' => 'integer', 'null' => false, 'length' => 3),
			'preferred_day' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'preferred_time' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'house_num_name' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'address1' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'address2' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'address3' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'postcode' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'brochure' => array('type' => 'integer', 'null' => false, 'default' => 0, 'length' => 3),
			'news_offers_optin' => array('type' => 'integer', 'null' => false, 'default' => 0, 'length' => 3),
			'num_adults' => array('type' => 'integer', 'null' => false, 'default' => 0, 'length' => 10),
			'num_children' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'child_age' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'preferred_date' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'preferred_duration' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'flexible' => array('type' => 'integer', 'null' => false, 'default' => 0, 'length' => 3),
			'airport' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'destinations' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'suggest' => array('type' => 'integer', 'null' => false, 'default' => 0, 'length' => 3),
			'accom_stars' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 32),
			'car_size' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'other_info' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'how_heard' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'newspaper' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $spotlights = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'name' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'slug' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'from_price' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'image_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'summary' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'details' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'date_and_price_information' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'dates_and_prices' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'meta_title' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'meta_description' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'meta_keywords' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'order' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'published' => array('type' => 'boolean', 'null' => false, 'default' => '0'),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $users = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'email' => array('type' => 'string', 'null' => false, 'key' => 'unique'),
			'password' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'group_id' => array('type' => 'integer', 'null' => false, 'length' => 10),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'email' => array('column' => 'email', 'unique' => 1))
		);
	var $webadmin_menu = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'parent_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'lft' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'rght' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'child_count' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'direct_child_count' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'text' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'aco_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $webadmin_user_profiles = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'user_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 10, 'key' => 'unique'),
			'name' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'last_login' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'user_id' => array('column' => 'user_id', 'unique' => 1))
		);
}
?>
