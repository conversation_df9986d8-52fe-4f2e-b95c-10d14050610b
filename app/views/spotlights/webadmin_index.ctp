<div id="spotlightsIndex" class="index">
  <div class="title clearfix">
    <h2><?php __('Spotlights');?></h2>
    <?php echo $this->element('webadmin_actions'); ?>
  </div>
  <?php
  $webAdmin->sessionFlash();
  if(!empty($spotlights)) :
    ?>
    <div class="table">
      <table cellpadding="0" cellspacing="0" id="spotlightsTable">
        <thead>
          <tr>
            <th class="reorder"></th>
            <th>Id</th>
            <th>Name</th>
            <th>Slug</th>
            <th>From Price</th>
            <th>Expiry Date</th>
            <th>Created</th>
            <th>Modified</th>
            <th class="actions"><?php __('Actions'); ?></th>
          </tr>
        </thead>
        <tbody>
          <?php
          $i = 0;
          foreach ($spotlights as $spotlight) :
            $class = null;
            if ($i++ % 2 == 0) {
              $class = ' class="altrow"';
            }
          ?>
          <tr<?php echo $class; ?> id="row-<?php echo $spotlight['Spotlight']['id']; ?>">
            <td><span class="drag">Drag</span></td>
            <td><?php echo $spotlight['Spotlight']['id']; ?></td>
            <td><?php echo $spotlight['Spotlight']['name']; ?></td>
            <td><?php echo $spotlight['Spotlight']['slug']; ?></td>
            <td><?php echo $spotlight['Spotlight']['from_price']; ?></td>
            <td><?php echo date("D, M jS Y", strtotime($spotlight['Spotlight']['expiry_date'])); ?></td>
            <td><?php echo $time->niceShort($spotlight['Spotlight']['created']); ?></td>
            <td><?php echo $time->niceShort($spotlight['Spotlight']['modified']); ?></td>
            <td class="actions">
              <?php echo $webAdmin->htmlLinkIfPermitted(__('Edit', true), array('action'=>'edit', $spotlight['Spotlight']['id']), array('class' => 'edit')); ?>
              <?php echo $webAdmin->htmlLinkIfPermitted(__('Delete', true), array('action'=>'delete', $spotlight['Spotlight']['id']), array('class' => 'delete'), sprintf(__('Are you sure you want to delete # %s?', true), $spotlight['Spotlight']['id'])); ?>
							<?php echo $webAdmin->htmlLinkIfPermitted(($spotlight['Spotlight']['published'] ? 'Unpublish' : 'Publish'), array('action' => 'toggle_field', 'published', $spotlight['Spotlight']['id'])); ?>
            </td>
          </tr>
          <?php endforeach; ?>
        </tbody>
      </table>
    </div>
    <?php echo $javascript->codeBlock("
      // Debug: Log all AJAX requests to see if any are calling toggle_field
      if (typeof Ajax !== 'undefined' && Ajax.Request) {
        var originalRequest = Ajax.Request.prototype.initialize;
        Ajax.Request.prototype.initialize = function(url, options) {
          console.log('[DEBUG] AJAX Request:', url, options);
          if (url.indexOf('toggle_field') !== -1) {
            console.log('[DEBUG] TOGGLE_FIELD AJAX detected:', url);
            console.trace();
          }
          return originalRequest.call(this, url, options);
        };
      }

      // Debug: Log all clicks on the page
      document.addEventListener('click', function(e) {
        if (e.target.href && e.target.href.indexOf('toggle_field') !== -1) {
          console.log('[DEBUG] Toggle field link clicked:', e.target.href);
          console.trace();
        }
      });

      new Webadmin.SortableTable('spotlightsTable', {controller: 'spotlights', model: 'Spotlight'});
    ", array('inline' => false)); ?>
  <?php else : ?>
    <p class="no-results">No results found</p>
  <?php endif; ?>
</div>
